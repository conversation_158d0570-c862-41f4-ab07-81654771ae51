<?php

class AV_PS_Data {
    private $plugin_dir;
    private $logs_dir;
    private $data_dir;
    private $data_parsed_dir;
    private $brands = ['mayoral', 'guess', 'abel-lula', 'mixed'];
    private $required_columns = [
        "сезон", "колекция", "Стил Код", "Цветови код", "Размер", "Име Цвят",
        "Допълнителен баркод 1", "Количество", "Категория", 
        "Продажна цена с ДДС Онлайн Магазини", "Намалена цена", 
        "Име на артикул", "Описание", "Brand"
    ];

    private $columns_reference = [ 
        'sku_columns_combination' => [
            "prefix" => "колекция", 
            "product_code" => "Стил Код", 
            "color_code" => "Цветови код", 
            "size" => "Размер",
        ],
        'sku_columns_combination_guess' => [
            "season" => "сезон", 
            "prefix" => "колекция", 
            "product_code" => "Стил Код", 
            "color_code" => "Цветови код", 
            "size" => "Размер",
        ],
        'color_columns' => [
            'color' => 'Име Цвят',
        ],
        'data_to_csv_columns' => [
            "prefix" => "колекция", 
            "product_code" => "Стил Код", 
            "color" => "Име Цвят",
            "color_code" => "Цветови код", 
            "size" => "Размер",
            "title" => "Име на артикул",
            "price" => "Продажна цена с ДДС Онлайн Магазини",
            "promo_price" => "Намалена цена",
            'quantity' => 'Количество',
            'category' => 'Категория',
            'barcode' => 'Допълнителен баркод 1',
            'description' => 'Описание',
            "season" => "сезон",
            "brand" => "Brand",
        ],
        'fixed_data' => [
            'brand_name' => 'Mayoral',
            'brand_slug' => 'mayoral',
        ],
        'data_columns' => [
            'season' => '__season__',
            'quantity' => '__quantity__',
            'price' => '__price__',
            'promo_price' => '__promo_price__',
            'barcode' => '__barcode__',
            'size' => '__size__',
            'color' => '__color__',
            'color_slug' => '__color_slug__',
            'color_variation' => '__color_variation__',
            'sku' => '__sku_variation__',
            'parent_sku' => '__sku_parent__',
            'brand_id' => '__brand_slug__',
        ],
    ];

    private $parent_sku_by_color = true;

    private $partial_options = [
        'sku', 
        'stock',
        'ean',
        'price',
        'main-image',
        'gallery',
        'attributes',
        'season',
        'category',
        'brand',
        'description',
    ];

    private $image_paths = [
        'mayoral' => ABSPATH . 'images-mayoral',
        'guess' => ABSPATH . 'images-guess',
        'abel-lula' => ABSPATH . 'image-abellula',
    ];

    private $image_data_dir = AV_PS_PLUGIN_PATH . 'data/images/';

    private $image_extensions = ['png', 'jpg', 'jpeg', 'gif', 'zip'];
    private $image_types = ['image/png', 'image/jpeg', 'image/gif', 'application/zip', 'application/x-zip-compressed'];

    public function __construct() {
        $this->plugin_dir = AV_PS_PLUGIN_PATH;
        $this->logs_dir = AV_PS_PLUGIN_PATH . 'logs/';
        $this->data_dir = AV_PS_PLUGIN_PATH . 'data/';
        $this->data_parsed_dir = AV_PS_PLUGIN_PATH . 'data/parsed/';

        if (!is_dir($this->data_dir)) {
            mkdir($this->data_dir, 0777, true);
        }

        if (!is_dir($this->data_parsed_dir)) {
            mkdir($this->data_parsed_dir, 0777, true);
        }

        if (!is_dir($this->image_data_dir)) {
            mkdir($this->image_data_dir, 0777, true);
        }

        foreach (array_keys($this->image_paths) as $brand) {
            if (!is_dir($this->image_paths[$brand])) {
                mkdir($this->image_paths[$brand], 0777, true);
            }
            if (!is_dir($this->image_data_dir . $brand)) {
                mkdir($this->image_data_dir . $brand, 0777, true);
            }
        }

    }

    public function getData($key = null) {
        if ($key === null) {
            return [
                'plugin_dir' => $this->plugin_dir,
                'logs_dir' => $this->logs_dir,
                'data_dir' => $this->data_dir,
                'data_parsed_dir' => $this->data_parsed_dir,
                'brands' => $this->brands,
                'required_columns' => $this->required_columns,
                'columns_reference' => $this->columns_reference,
                'image_paths' => $this->image_paths,
                'image_data_dir' => $this->image_data_dir,
                'image_extensions' => $this->image_extensions,
                'image_types' => $this->image_types,
            ];
        }

        $is_single = false;
        $last_key = '';
        $keys = [];
        if(is_string($key)) {
            $keys = [$key];
            $is_single = true;
            $last_key = $key;
        }
        else if(is_array($key)) {
            $keys = $key;
            $is_single = count($keys) === 1;
        }

        $return_data = [];
        foreach ($keys as $key) {
            $parts = explode(':', $key);
            $data = $this;
            $last_key = $key;
            foreach ($parts as $part) {
                if (is_object($data) && property_exists($data, $part)) {
                    $data = $data->$part;
                } elseif (is_array($data) && array_key_exists($part, $data)) {
                    $data = $data[$part];
                } else {
                    $data = null;
                    break;
                }
            }
            $return_data[$key] = $data;
        }

        if($is_single) {
            return $return_data[$last_key] ?? null;
        }
    
        return $return_data;
    }

    public function getSKUcolumnsCombinations($brand) {
        if(!empty($brand) && $brand == 'guess') {
            return $this->columns_reference['sku_columns_combination_guess'];
        }
        return $this->columns_reference['sku_columns_combination'];
    }

    public function getCSVcolumnName($column='') {
        return $this->columns_reference['data_to_csv_columns'][$column] ?? null;
    }
    
}

global $AV_PS_Data;
if(!function_exists('AV_PS_Data')) {
    function AV_PS_Data() {
        global $AV_PS_Data;
        if(!isset($AV_PS_Data)) {
            $AV_PS_Data = new AV_PS_Data();
        }
        return $AV_PS_Data;
    }
}