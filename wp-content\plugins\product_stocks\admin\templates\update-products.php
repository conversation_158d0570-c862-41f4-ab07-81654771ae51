<?php
    $nonce = wp_create_nonce('_ps_update_nonce_update_products');
?>

<div id="ps_update_products_page">
<h2>Обновяване на продукти от CSV файл</h2>
<form id="ps_update_form" action="" method="post">
<table class="wp-list-table widefat fixed striped ps_update_products">
    <tbody>
        <tr>
            <th scope="row"><label for="brand-select">Бранд:</label></th>
            <td class="dis-area">
                <select id="brand-select" class="regular-text">
                    <option value="mayoral">Mayoral</option>
                    <option value="guess">Guess</option>
                    <option value="abel-lula">Abel & Lula</option>
                </select>
            </td>
        </tr>
        <tr>
            <th scope="row"><label for="update-type">Вид обновяване:</label></th>
            <td class="dis-area">
                <select id="update-type" class="regular-text">
                    <option value="all">Съществуващи и нови продукти</option>
                    <option value="new">Само нови продукти</option>
                    <option value="existing">Само съществуващи продукти</option>
                </select>
            </td>
        </tr>
        <tr id="update-data-row">
            <th scope="row">Данни за обновяване:</th>
            <td class="dis-area">
                <fieldset>
                    <legend class="screen-reader-text"><span>Режим на обновяване</span></legend>
                    <label><input type="radio" name="update-mode" value="full" checked> Пълно обновяване</label><br>
                    <label><input type="radio" name="update-mode" value="partial"> Частично обновяване</label>
                </fieldset>
                <fieldset id="partial-options" style="display: none;">
                    <legend class="screen-reader-text"><span>Опции за частично обновяване</span></legend>
                    <label><input type="checkbox" name="update-option" value="sku"> SKU</label><br>
                    <label><input type="checkbox" name="update-option" value="stock"> Наличност</label><br>
                    <label><input type="checkbox" name="update-option" value="ean"> EAN код</label><br>
                    <label><input type="checkbox" name="update-option" value="price"> Цена</label><br>
                    <label><input type="checkbox" name="update-option" value="main-image"> Основна снимка</label><br>
                    <label><input type="checkbox" name="update-option" value="gallery"> Галерия</label><br>
                    <label><input type="checkbox" name="update-option" value="attributes"> Атрибути</label><br>
                    <label><input type="checkbox" name="update-option" value="season"> Сезон</label><br>
                    <label><input type="checkbox" name="update-option" value="category"> Категория</label><br>
                    <label><input type="checkbox" name="update-option" value="brand"> Бранд</label><br>
                    <label><input type="checkbox" name="update-option" value="description"> Описание</label><br>
                </fieldset>
            </td>
        </tr>
        <tr id="csv-file-row">
            <th scope="row"><label for="csv-file">CSV файл:</label></th>
            <td class="dis-area">
                <div class="wrap" style="position: relative; z-index: 1;">
                    <div class="ps-update-uploader">
                        <h3>Качване на CSV файл</h3>
                        <input type="file" name="ps_update_file" id="ps_update_file" class="ps-update-file-input" accept=".csv">
                        <label for="ps_update_file" class="ps-update-file-label">Изберете файл</label>
                        <div class="ps-update-file-name"></div>
                        <div class="ps-update-progress" style="display:none;">
                            <div class="ps-update-progress-bar"></div>
                        </div>
                        <div class="file-upload-result-container"></div>
                    </div>
                </div>

                <div id="file-list" class="__*update-nag*__">


                </div>
            </td>
        </tr>

        <?php 
            
            if(isDeveloper()) {
                $test_mode = get_option('ps_test_mode_update_products', 'yes');
                ?>
                <tr>
                    <th scope="row"><label for="test-mode">Тест режим:</label></th>
                    <td class="dis-area" style="display: flex; justify-content: space-between;">
                        <select id="test-mode" class="regular-text">
                            <option value="yes" <?php echo ($test_mode == 'yes') ? 'selected' : ''; ?>>Да</option>
                            <option value="no" <?php echo ($test_mode == 'no') ? 'selected' : ''; ?>>Не</option>
                        </select>

                        <button id="test-data" class="button button-primary">Тест данни</button>
                    </td>
                </tr>
                <?php
            }
            else {
                echo '<input type="hidden" id="test-mode" value="no">';
            }
            
            ?>

        <tr id="update-result-row" style="display: none;">
            <td colspan="2" style="position: relative;">
            </td>
        </tr>
        <tr id="update-button-row" style="display: none;">
            <td colspan="2">
                <input type="hidden" id="ps_update_nonce" value="<?php echo $nonce; ?>">    
                <button id="start-update" class="button button-primary">Започни обновяването</button>
            </td>
        </tr>

    </tbody>
</table>
</form>

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
    <symbol id="success-icon" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
    </symbol>
    <symbol id="error-icon" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
    </symbol>
    <symbol id="refresh-icon" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
    </symbol>
  <symbol id="delete-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M3 6H5H21"></path>
    <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"></path>
  </symbol>
</svg>

</div>